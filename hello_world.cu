#include <stdio.h>
#include <cuda_runtime.h>

__global__ void helloWorldKernel() {
    // 获取线程块编号
    int blockId = blockIdx.x;
    
    // 获取二维块内线程编号
    int threadX = threadIdx.x;
    int threadY = threadIdx.y;
    
    // 输出指定格式的信息
    printf("Hello World from Thread (%d, %d) in Block %d!\n", 
           threadX, threadY, blockId);
}

int main() {
    int n, m, k;
    
    // 输入参数
    printf("请输入三个整数 n, m, k (取值范围[1,32]): ");
    scanf("%d %d %d", &n, &m, &k);
    
    // 验证输入范围
    if (n < 1 || n > 32 || m < 1 || m > 32 || k < 1 || k > 32) {
        printf("错误：输入参数必须在[1,32]范围内\n");
        return -1;
    }
    
    // 主线程输出
    printf("Hello World from the host!\n");
    
    // 配置CUDA执行参数
    dim3 blockSize(m, k);  // 每个线程块的维度为m×k
    dim3 gridSize(n);      // 创建n个线程块
    
    printf("\n配置信息：\n");
    printf("线程块数量: %d\n", n);
    printf("每个线程块维度: %d × %d\n", m, k);
    printf("总线程数: %d\n", n * m * k);
    printf("\n线程输出：\n");
    
    // 启动CUDA kernel
    helloWorldKernel<<<gridSize, blockSize>>>();
    
    // 等待GPU完成
    cudaDeviceSynchronize();
    
    // 检查CUDA错误
    cudaError_t error = cudaGetLastError();
    if (error != cudaSuccess) {
        printf("CUDA错误: %s\n", cudaGetErrorString(error));
        return -1;
    }
    
    printf("\n实验完成！\n");
    printf("观察：线程输出顺序可能不规律，这是因为GPU中的线程执行是并行的，\n");
    printf("不同线程块和线程的执行顺序不确定。\n");
    
    return 0;
}
