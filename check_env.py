# check_env.py
import pycuda.autoinit
from pycuda import driver
import sys

def check_environment():
    print("=" * 50)
    print("CUDA环境验证")
    print("=" * 50)
    
    # 检查CUDA设备
    device_count = driver.Device.count()
    if device_count == 0:
        print("错误：未找到支持CUDA的NVIDIA GPU设备")
        return False
    
    print(f"找到 {device_count} 个CUDA设备:")
    for i in range(device_count):
        dev = driver.Device(i)
        print(f"\n设备 {i}: {dev.name()}")
        print(f" 计算能力: {dev.compute_capability()}")
        print(f" 总显存: {dev.total_memory()/1024**3:.2f} GB")
        
        # 检查计算能力是否支持printf
        compute_cap = float(f"{dev.compute_capability()[0]}.{dev.compute_capability()[1]}")
        if compute_cap < 2.0:
            print(f"警告：设备计算能力 {compute_cap} < 2.0，可能不支持printf")
    
    # 检查PyCUDA版本
    import pycuda
    print(f"\nPyCUDA版本: {pycuda.VERSION_TEXT}")
    
    # 检查CU    
if __name__ == "__main__":
    check_environment()