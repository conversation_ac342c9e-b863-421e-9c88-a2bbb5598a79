#include <iostream>
#include <vector>
#include <chrono>
#include <random>
#include <thread>
#include <cmath>
#include <iomanip>

using namespace std;
using namespace std::chrono;

// 矩阵转置函数
void transposeMatrix(const vector<vector<float>>& input, vector<vector<float>>& output, int n) {
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            output[j][i] = input[i][j];
        }
    }
}

// 并行矩阵转置（模拟CUDA并行）
void parallelTranspose(const vector<vector<float>>& input, vector<vector<float>>& output, int n) {
    const int num_threads = thread::hardware_concurrency();
    vector<thread> threads;
    
    auto worker = [&](int start_row, int end_row) {
        for (int i = start_row; i < end_row; i++) {
            for (int j = 0; j < n; j++) {
                output[j][i] = input[i][j];
            }
        }
    };
    
    int rows_per_thread = n / num_threads;
    for (int t = 0; t < num_threads; t++) {
        int start_row = t * rows_per_thread;
        int end_row = (t == num_threads - 1) ? n : (t + 1) * rows_per_thread;
        threads.emplace_back(worker, start_row, end_row);
    }
    
    for (auto& t : threads) {
        t.join();
    }
}

// 初始化矩阵
void initMatrix(vector<vector<float>>& matrix, int n) {
    random_device rd;
    mt19937 gen(rd());
    uniform_real_distribution<float> dis(0.0f, 10.0f);
    
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            matrix[i][j] = dis(gen);
        }
    }
}

// 打印矩阵
void printMatrix(const vector<vector<float>>& matrix, int n, const string& name) {
    if (n <= 8) {
        cout << name << ":" << endl;
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                cout << fixed << setprecision(1) << setw(6) << matrix[i][j] << " ";
            }
            cout << endl;
        }
        cout << endl;
    }
}

// 验证结果
bool verifyResult(const vector<vector<float>>& result1, const vector<vector<float>>& result2, int n) {
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            if (abs(result1[i][j] - result2[i][j]) > 1e-5) {
                return false;
            }
        }
    }
    return true;
}

int main() {
    int n;
    cout << "请输入矩阵大小 n (取值范围[512, 2048]): ";
    cin >> n;
    
    if (n < 512 || n > 2048) {
        cout << "错误：矩阵大小必须在[512, 2048]范围内" << endl;
        return -1;
    }
    
    cout << "矩阵大小: " << n << " × " << n << endl;
    
    // 分配矩阵
    vector<vector<float>> input(n, vector<float>(n));
    vector<vector<float>> output_serial(n, vector<float>(n));
    vector<vector<float>> output_parallel(n, vector<float>(n));
    
    // 初始化矩阵
    initMatrix(input, n);
    printMatrix(input, n, "原始矩阵 A");
    
    // 串行转置
    auto start = high_resolution_clock::now();
    transposeMatrix(input, output_serial, n);
    auto end = high_resolution_clock::now();
    auto serial_time = duration_cast<milliseconds>(end - start).count();
    
    printMatrix(output_serial, n, "串行转置结果 A^T");
    
    // 并行转置
    start = high_resolution_clock::now();
    parallelTranspose(input, output_parallel, n);
    end = high_resolution_clock::now();
    auto parallel_time = duration_cast<milliseconds>(end - start).count();
    
    printMatrix(output_parallel, n, "并行转置结果 A^T");
    
    // 验证结果
    if (verifyResult(output_serial, output_parallel, n)) {
        cout << "✓ 并行转置结果正确" << endl;
    } else {
        cout << "✗ 并行转置结果错误" << endl;
    }
    
    // 性能分析
    cout << "\n性能测试结果：" << endl;
    cout << "串行转置时间: " << serial_time << " ms" << endl;
    cout << "并行转置时间: " << parallel_time << " ms" << endl;
    
    if (parallel_time > 0) {
        cout << "加速比: " << fixed << setprecision(2) 
             << (double)serial_time / parallel_time << "x" << endl;
    }
    
    cout << "使用CPU线程数: " << thread::hardware_concurrency() << endl;
    
    return 0;
}
