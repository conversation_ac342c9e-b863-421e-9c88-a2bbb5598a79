# CUDA并行程序设计实验

本项目包含两个CUDA实验的完整实现：Hello World和矩阵转置。

## 文件结构

```
├── hello_world.cu              # CUDA Hello World程序
├── matrix_transpose.cu         # CUDA矩阵转置程序
├── hello_world_cpp.cpp         # C++ Hello World程序（模拟版本）
├── matrix_transpose_cpp.cpp    # C++ 矩阵转置程序（模拟版本）
├── test_hello.cpp              # 简化的测试程序
├── Makefile                    # 编译脚本
├── run_experiments.bat         # Windows批处理运行脚本
├── 实验报告.md                 # 详细实验报告
├── 实验结果.txt                # 实验运行结果
└── README.md                   # 本文件
```

## 实验内容

### 实验1：CUDA Hello World
- 创建多个线程块，每个线程输出指定格式信息
- 观察GPU并行执行的特性
- 分析线程输出顺序的随机性

### 实验2：CUDA矩阵转置
- 实现朴素矩阵转置算法
- 实现共享内存优化的转置算法
- 性能对比和分析

## 编译和运行

### 方法1：使用CUDA编译器（推荐）

如果系统安装了CUDA Toolkit：

```bash
# 编译Hello World程序
nvcc -O2 -arch=sm_50 -o hello_world hello_world.cu

# 编译矩阵转置程序
nvcc -O2 -arch=sm_50 -o matrix_transpose matrix_transpose.cu

# 运行程序
./hello_world
./matrix_transpose
```

### 方法2：使用C++编译器（模拟版本）

如果没有CUDA环境，可以运行C++模拟版本：

```bash
# 使用g++编译
g++ -O2 -std=c++11 -pthread -o hello_world_cpp hello_world_cpp.cpp
g++ -O2 -std=c++11 -pthread -o matrix_transpose_cpp matrix_transpose_cpp.cpp

# 使用Visual Studio编译器（Windows）
cl /EHsc /O2 hello_world_cpp.cpp
cl /EHsc /O2 matrix_transpose_cpp.cpp
```

### 方法3：使用Makefile

```bash
# 编译所有程序
make all_versions

# 只编译C++版本
make all

# 运行测试
make test
```

### 方法4：使用批处理脚本（Windows）

```cmd
run_experiments.bat
```

## 输入参数

### Hello World程序
- n: 线程块数量 (1-32)
- m: 线程块X维度 (1-32)  
- k: 线程块Y维度 (1-32)

示例输入：`2 3 2`

### 矩阵转置程序
- n: 矩阵大小 (512-2048)

示例输入：`1024`

## 实验结果

程序会输出：
1. 线程执行信息和配置参数
2. 原始矩阵和转置结果（小矩阵）
3. 性能测试结果和加速比分析
4. 算法正确性验证

## 性能分析

实验测试了以下因素对性能的影响：
- 不同线程块大小 (8×8, 16×16, 32×32)
- 不同矩阵规模 (512², 1024², 2048²)
- 不同算法策略 (朴素算法 vs 共享内存优化)
- CPU vs GPU性能对比

## 技术特点

### CUDA版本特点
- 使用CUDA Runtime API
- 实现全局内存和共享内存两种访问模式
- 包含完整的错误检查和内存管理
- 支持不同GPU架构

### C++版本特点
- 使用std::thread模拟并行执行
- 提供性能对比基准
- 便于算法验证和调试
- 跨平台兼容性好

## 注意事项

1. CUDA程序需要NVIDIA GPU和CUDA Toolkit支持
2. C++程序需要支持C++11标准的编译器
3. Windows环境可能需要设置Visual Studio环境变量
4. 大矩阵测试需要足够的GPU内存

## 实验报告

详细的实验分析和结果请参考 `实验报告.md` 文件，包含：
- 算法设计思路
- 代码实现分析
- 性能测试结果
- 优化策略讨论
- 实验结论和心得

## 作者信息

- 学号：22336080
- 姓名：郭家成
- 课程：并行程序设计与算法
- 实验：8-CUDA矩阵转置
