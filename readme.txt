并行程序设计与算法实验
8-CUDA矩阵转置
提交格式说明
按照实验报告模板填写报告，需要提供源代码及代码描述至https://easyhpc.net/course/221。实验报告模板使用PDF格式，命名方式为“并行程序设计_学号_姓名”。如有疑问，在课程群（群号1021067950）中询问细节。
1. CUDA Hello World
本实验为CUDA入门练习，由多个线程并行输出“Hello World！”。
输入：三个整数n,m,k，其取值范围为[1,32]
问题描述：创建n个线程块，每个线程块的维度为m×k，每个线程均输出线程块编号、二维块内线程编号及Hello World！（如，“Hello World from Thread (1, 2) in Block 10!”）。主线程输出“Hello World from the host!”。
要求：完成上述内容，观察输出，并回答线程输出顺序是否有规律？
2. CUDA矩阵转置
使用CUDA对矩阵进行并行转置。
输入：整数n，其取值范围均为[512, 2048]
问题描述：随机生成n×n的矩阵A，对其进行转置得到A^T。转置矩阵中第i行j列上的元素为原矩阵中j行i列元素，即A_ij^T=A_ji。
输出：矩阵A及其转置矩阵A^T，及计算所消耗的时间t。
要求：使用CUDA实现并行矩阵转置，分析不同线程块大小，矩阵规模，访存方式，任务/数据划分方式，对程序性能的影响。
