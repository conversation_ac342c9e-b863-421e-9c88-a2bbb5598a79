# 编译器和解释器
NVCC = nvcc
PYTHON = python

# 编译选项
NVCCFLAGS = -O2 -arch=sm_50

# 目标文件
CUDA_TARGETS = hello_world matrix_transpose
PYTHON_TARGETS = hello_world.py matrix_transpose.py
ALL_TARGETS = $(CUDA_TARGETS)

# 默认目标（运行Python版本，因为更通用）
all: test_python

# 编译CUDA版本
cuda: $(CUDA_TARGETS)

# CUDA版本
hello_world: hello_world.cu
	$(NVCC) $(NVCCFLAGS) -o hello_world hello_world.cu

matrix_transpose: matrix_transpose.cu
	$(NVCC) $(NVCCFLAGS) -o matrix_transpose matrix_transpose.cu

# 测试Python版本
test_hello_python:
	@echo "=== 测试Hello World Python程序 ==="
	@echo "2 3 2" | $(PYTHON) hello_world.py

test_matrix_python:
	@echo "=== 测试矩阵转置Python程序 ==="
	@echo "512" | $(PYTHON) matrix_transpose.py

# 测试CUDA版本
test_hello_cuda: hello_world
	@echo "=== 测试Hello World CUDA程序 ==="
	@echo "2 3 2" | ./hello_world

test_matrix_cuda: matrix_transpose
	@echo "=== 测试矩阵转置CUDA程序 ==="
	@echo "512" | ./matrix_transpose

# 运行所有测试
test_python: test_hello_python test_matrix_python

test_cuda: test_hello_cuda test_matrix_cuda

# 清理编译文件
clean:
	rm -f $(ALL_TARGETS) *.exe

.PHONY: all cuda test_python test_cuda test_hello_python test_matrix_python test_hello_cuda test_matrix_cuda clean
