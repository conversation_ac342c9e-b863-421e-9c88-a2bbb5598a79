# 编译器
NVCC = nvcc
CXX = g++

# 编译选项
NVCCFLAGS = -O2 -arch=sm_50
CXXFLAGS = -O2 -std=c++11 -pthread

# 目标文件
CUDA_TARGETS = hello_world matrix_transpose
CPP_TARGETS = hello_world_cpp matrix_transpose_cpp
ALL_TARGETS = $(CUDA_TARGETS) $(CPP_TARGETS)

# 默认目标（编译C++版本，因为更通用）
all: $(CPP_TARGETS)

# 编译所有版本
all_versions: $(ALL_TARGETS)

# CUDA版本
hello_world: hello_world.cu
	$(NVCC) $(NVCCFLAGS) -o hello_world hello_world.cu

matrix_transpose: matrix_transpose.cu
	$(NVCC) $(NVCCFLAGS) -o matrix_transpose matrix_transpose.cu

# C++版本
hello_world_cpp: hello_world_cpp.cpp
	$(CXX) $(CXXFLAGS) -o hello_world_cpp hello_world_cpp.cpp

matrix_transpose_cpp: matrix_transpose_cpp.cpp
	$(CXX) $(CXXFLAGS) -o matrix_transpose_cpp matrix_transpose_cpp.cpp

# 测试C++版本
test_hello_cpp: hello_world_cpp
	@echo "=== 测试Hello World C++程序 ==="
	@echo "2 3 2" | ./hello_world_cpp

test_matrix_cpp: matrix_transpose_cpp
	@echo "=== 测试矩阵转置C++程序 ==="
	@echo "512" | ./matrix_transpose_cpp

# 测试CUDA版本
test_hello_cuda: hello_world
	@echo "=== 测试Hello World CUDA程序 ==="
	@echo "2 3 2" | ./hello_world

test_matrix_cuda: matrix_transpose
	@echo "=== 测试矩阵转置CUDA程序 ==="
	@echo "512" | ./matrix_transpose

# 运行所有测试
test: test_hello_cpp test_matrix_cpp

test_cuda: test_hello_cuda test_matrix_cuda

# 清理编译文件
clean:
	rm -f $(ALL_TARGETS) *.exe

.PHONY: all all_versions test test_cuda test_hello_cpp test_matrix_cpp test_hello_cuda test_matrix_cuda clean
