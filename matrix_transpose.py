#!/usr/bin/env python3
"""
CUDA矩阵转置Python实现
使用NumPy和多线程实现高性能并行矩阵转置
"""

import numpy as np
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

class MatrixTranspose:
    def __init__(self, n):
        self.n = n
        self.tile_size = 16  # 模拟CUDA的TILE_SIZE
        
    def cpu_transpose(self, matrix):
        """CPU串行转置"""
        return matrix.T
    
    def naive_parallel_transpose(self, matrix):
        """朴素并行转置 - 模拟CUDA朴素算法"""
        result = np.zeros((self.n, self.n), dtype=np.float32)
        
        def transpose_block(start_row, end_row):
            for i in range(start_row, end_row):
                for j in range(self.n):
                    result[j, i] = matrix[i, j]
        
        # 使用多线程并行处理
        num_threads = min(multiprocessing.cpu_count(), 8)
        rows_per_thread = self.n // num_threads
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = []
            for t in range(num_threads):
                start_row = t * rows_per_thread
                end_row = self.n if t == num_threads - 1 else (t + 1) * rows_per_thread
                future = executor.submit(transpose_block, start_row, end_row)
                futures.append(future)
            
            # 等待所有线程完成
            for future in futures:
                future.result()
        
        return result
    
    def tiled_parallel_transpose(self, matrix):
        """分块并行转置 - 模拟CUDA共享内存优化"""
        result = np.zeros((self.n, self.n), dtype=np.float32)
        
        def transpose_tile(tile_row, tile_col):
            # 计算分块边界
            start_row = tile_row * self.tile_size
            end_row = min(start_row + self.tile_size, self.n)
            start_col = tile_col * self.tile_size
            end_col = min(start_col + self.tile_size, self.n)
            
            # 提取分块到临时数组（模拟共享内存）
            tile = matrix[start_row:end_row, start_col:end_col]
            
            # 转置分块
            transposed_tile = tile.T
            
            # 写入结果矩阵的对应位置
            result[start_col:end_col, start_row:end_row] = transposed_tile
        
        # 计算需要的分块数量
        num_tiles_row = (self.n + self.tile_size - 1) // self.tile_size
        num_tiles_col = (self.n + self.tile_size - 1) // self.tile_size
        
        # 并行处理所有分块
        with ThreadPoolExecutor(max_workers=multiprocessing.cpu_count()) as executor:
            futures = []
            for tile_row in range(num_tiles_row):
                for tile_col in range(num_tiles_col):
                    future = executor.submit(transpose_tile, tile_row, tile_col)
                    futures.append(future)
            
            # 等待所有分块完成
            for future in futures:
                future.result()
        
        return result
    
    def numpy_optimized_transpose(self, matrix):
        """NumPy优化转置（使用底层BLAS）"""
        return np.transpose(matrix)

def init_matrix(n):
    """初始化随机矩阵"""
    np.random.seed(42)  # 固定种子确保可重复性
    return np.random.rand(n, n).astype(np.float32) * 10

def print_matrix(matrix, name, max_size=8):
    """打印矩阵（仅小矩阵）"""
    n = matrix.shape[0]
    if n <= max_size:
        print(f"{name}:")
        for i in range(n):
            for j in range(n):
                print(f"{matrix[i, j]:6.1f}", end=" ")
            print()
        print()

def verify_result(result1, result2, tolerance=1e-5):
    """验证结果正确性"""
    return np.allclose(result1, result2, atol=tolerance)

def benchmark_function(func, matrix, name):
    """性能测试函数"""
    # 预热运行
    _ = func(matrix)

    # 多次运行取平均值提高精度
    num_runs = 5
    total_time = 0

    for _ in range(num_runs):
        start_time = time.perf_counter()
        result = func(matrix)
        end_time = time.perf_counter()
        total_time += (end_time - start_time)

    elapsed_time = (total_time / num_runs) * 1000  # 转换为毫秒
    print(f"{name}: {elapsed_time:.3f} ms")
    return result, elapsed_time

def main():
    print("CUDA矩阵转置Python实现")
    print("=" * 50)
    
    # 获取矩阵大小
    try:
        n = int(input("请输入矩阵大小 n (取值范围[512, 2048]): "))
    except ValueError:
        print("输入格式错误，使用默认值: n=1024")
        n = 1024
    
    if not (512 <= n <= 2048):
        print("错误：矩阵大小必须在[512, 2048]范围内")
        return -1
    
    print(f"矩阵大小: {n} × {n}")
    print(f"使用CPU核心数: {multiprocessing.cpu_count()}")
    
    # 初始化矩阵
    matrix = init_matrix(n)
    print_matrix(matrix, "原始矩阵 A")
    
    # 创建转置器
    transposer = MatrixTranspose(n)
    
    print("\n性能测试结果：")
    print("-" * 40)
    
    # CPU串行转置
    cpu_result, cpu_time = benchmark_function(
        transposer.cpu_transpose, matrix, "CPU串行转置"
    )
    print_matrix(cpu_result, "CPU转置结果 A^T")
    
    # 朴素并行转置
    naive_result, naive_time = benchmark_function(
        transposer.naive_parallel_transpose, matrix, "朴素并行转置"
    )
    
    # 验证结果
    if verify_result(cpu_result, naive_result):
        print("  ✓ 朴素并行转置结果正确")
    else:
        print("  ✗ 朴素并行转置结果错误")
    
    # 分块并行转置
    tiled_result, tiled_time = benchmark_function(
        transposer.tiled_parallel_transpose, matrix, "分块并行转置"
    )
    
    # 验证结果
    if verify_result(cpu_result, tiled_result):
        print("  ✓ 分块并行转置结果正确")
    else:
        print("  ✗ 分块并行转置结果错误")
    
    # NumPy优化转置
    numpy_result, numpy_time = benchmark_function(
        transposer.numpy_optimized_transpose, matrix, "NumPy优化转置"
    )
    
    # 验证结果
    if verify_result(cpu_result, numpy_result):
        print("  ✓ NumPy优化转置结果正确")
    else:
        print("  ✗ NumPy优化转置结果错误")
    
    print_matrix(numpy_result, "最终转置结果 A^T")
    
    # 性能分析
    print("\n加速比分析：")
    print("-" * 40)
    if naive_time > 0:
        print(f"朴素并行相对CPU加速比: {cpu_time / naive_time:.2f}x")
    if tiled_time > 0:
        print(f"分块并行相对CPU加速比: {cpu_time / tiled_time:.2f}x")
    if numpy_time > 0:
        print(f"NumPy优化相对CPU加速比: {cpu_time / numpy_time:.2f}x")
    
    if tiled_time > 0 and naive_time > 0:
        print(f"分块相对朴素算法加速比: {naive_time / tiled_time:.2f}x")
    
    print(f"\n算法特点分析：")
    print("- CPU串行：单线程顺序执行")
    print("- 朴素并行：多线程直接并行，模拟CUDA朴素算法")
    print("- 分块并行：使用分块技术优化内存访问，模拟CUDA共享内存优化")
    print("- NumPy优化：使用高度优化的BLAS库，代表最佳性能基准")
    
    return 0

if __name__ == "__main__":
    main()
