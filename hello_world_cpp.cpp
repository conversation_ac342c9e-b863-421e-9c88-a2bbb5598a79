#include <iostream>
#include <thread>
#include <vector>
#include <mutex>

std::mutex print_mutex;

// 模拟CUDA线程的函数
void simulateThread(int blockId, int threadX, int threadY) {
    std::lock_guard<std::mutex> lock(print_mutex);
    std::cout << "Hello World from Thread (" << threadX << ", " << threadY 
              << ") in Block " << blockId << "!" << std::endl;
}

int main() {
    int n, m, k;
    
    std::cout << "请输入三个整数 n, m, k (取值范围[1,32]): ";
    std::cin >> n >> m >> k;
    
    if (n < 1 || n > 32 || m < 1 || m > 32 || k < 1 || k > 32) {
        std::cout << "错误：输入参数必须在[1,32]范围内" << std::endl;
        return -1;
    }
    
    std::cout << "Hello World from the host!" << std::endl;
    
    std::cout << "\n配置信息：" << std::endl;
    std::cout << "线程块数量: " << n << std::endl;
    std::cout << "每个线程块维度: " << m << " × " << k << std::endl;
    std::cout << "总线程数: " << n * m * k << std::endl;
    std::cout << "\n线程输出：" << std::endl;
    
    // 创建线程模拟CUDA执行
    std::vector<std::thread> threads;
    
    for (int blockId = 0; blockId < n; blockId++) {
        for (int threadX = 0; threadX < m; threadX++) {
            for (int threadY = 0; threadY < k; threadY++) {
                threads.emplace_back(simulateThread, blockId, threadX, threadY);
            }
        }
    }
    
    // 等待所有线程完成
    for (auto& t : threads) {
        t.join();
    }
    
    std::cout << "\n实验完成！" << std::endl;
    std::cout << "观察：线程输出顺序可能不规律，这是因为线程执行是并行的，" << std::endl;
    std::cout << "不同线程的执行顺序不确定。" << std::endl;
    
    return 0;
}
