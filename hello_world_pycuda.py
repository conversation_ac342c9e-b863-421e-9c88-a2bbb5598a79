
import pycuda.autoinit
import pycuda.driver as drv
from pycuda.compiler import SourceModule
import numpy as np
import os
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
def cuda_hello_world():
    n, m, k = 2, 3, 2
    

    # 主机输出
    print("\nHello World from the host!")
    print(f"\n配置信息：线程块数量={n}, 每个线程块维度={m}x{k}, 总线程数={n*m*k}")
    
    # 设置环境变量确保printf可用
    os.environ["PYCUDA_ENABLE_CUDA_PRINT"] = "1"
    
    # CUDA核函数代码
    kernel_code = """
    #include <stdio.h>
    
    __global__ void hello_kernel()
    {
        // 获取当前线程在网格中的位置
        int block_id = blockIdx.x;
        int thread_x = threadIdx.x;
        int thread_y = threadIdx.y;
        
        // 输出信息
        printf("Hello World from Thread (%d, %d) in Block %d!\\n", 
               thread_x, thread_y, block_id);
    }
    """
    
    # 编译并加载核函数
    try:
        mod = SourceModule(kernel_code, options=['-Wno-deprecated-gpu-targets'])
        hello_kernel = mod.get_function("hello_kernel")
    except Exception as e:
        print(f"编译CUDA核函数时出错: {e}")
        return
    
    
    # 启动核函数
    try:
        # 网格配置: n个线程块
        # 线程块配置: m x k 二维线程块
        hello_kernel(block=(m, k, 1), grid=(n, 1))
        
        # 等待所有线程完成
        drv.Context.synchronize()
    except Exception as e:
        print(f"执行CUDA核函数时出错: {e}")
        return
    
    print(f"\n实验完成！")

if __name__ == "__main__":
    cuda_hello_world()