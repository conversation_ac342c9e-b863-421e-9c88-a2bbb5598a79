#!/usr/bin/env python3
"""
CUDA Hello World Python实现
使用多线程模拟CUDA的并行执行模型
"""

import threading
import time
import random
from concurrent.futures import ThreadPoolExecutor

# 全局锁用于同步输出
print_lock = threading.Lock()

def cuda_thread_function(block_id, thread_x, thread_y):
    """
    模拟CUDA kernel函数
    每个线程输出指定格式的信息
    """
    # 添加随机延迟模拟GPU线程调度的不确定性
    time.sleep(random.uniform(0.001, 0.01))
    
    with print_lock:
        print(f"Hello World from Thread ({thread_x}, {thread_y}) in Block {block_id}!")

def main():
    print("CUDA Hello World Python实现")
    print("=" * 50)
    
    # 获取输入参数
    try:
        n, m, k = map(int, input("请输入三个整数 n, m, k (取值范围[1,32]): ").split())
    except ValueError:
        print("输入格式错误，使用默认值: n=2, m=3, k=2")
        n, m, k = 2, 3, 2
    
    # 验证输入范围
    if not (1 <= n <= 32 and 1 <= m <= 32 and 1 <= k <= 32):
        print("错误：输入参数必须在[1,32]范围内")
        return -1
    
    # 主线程输出
    print("Hello World from the host!")
    
    # 配置信息
    print(f"\n配置信息：")
    print(f"线程块数量: {n}")
    print(f"每个线程块维度: {m} × {k}")
    print(f"总线程数: {n * m * k}")
    print(f"\n线程输出：")
    
    # 创建线程池模拟CUDA执行
    max_workers = min(32, n * m * k)  # 限制并发线程数
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有线程任务
        futures = []
        for block_id in range(n):
            for thread_x in range(m):
                for thread_y in range(k):
                    future = executor.submit(cuda_thread_function, block_id, thread_x, thread_y)
                    futures.append(future)
        
        # 等待所有线程完成
        for future in futures:
            future.result()
    
    print(f"\n实验完成！")
    print("观察：线程输出顺序可能不规律，这是因为线程执行是并行的，")
    print("不同线程块和线程的执行顺序不确定，这模拟了GPU的并行特性。")
    
    return 0

if __name__ == "__main__":
    main()
