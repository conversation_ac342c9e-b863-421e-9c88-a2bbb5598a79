
import pycuda.autoinit
import pycuda.driver as cuda
from pycuda.compiler import SourceModule
import pycuda.gpuarray as gpuarray
import numpy as np
import time
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
TILE_SIZE = 16

def main():
    print("CUDA矩阵转置PyCUDA实现")
    print("=" * 50)
    
    n = 1024

    print(f"矩阵大小: {n} x {n}")
    
    # 初始化矩阵
    np.random.seed(42)
    h_input = np.random.rand(n, n).astype(np.float32)
    
    # 打印小矩阵
    if n <= 8:
        print("原始矩阵 A:")
        for i in range(n):
            for j in range(n):
                print(f"{h_input[i, j]:6.1f}", end=" ")
            print()
        print()
    
    # CUDA kernel代码
    kernel_code = f"""
    #define TILE_SIZE {TILE_SIZE}
    
    // 朴素矩阵转置kernel
    __global__ void naiveTranspose(float* input, float* output, int n) {{
        int col = blockIdx.x * blockDim.x + threadIdx.x;
        int row = blockIdx.y * blockDim.y + threadIdx.y;
        
        if (row < n && col < n) {{
            output[col * n + row] = input[row * n + col];
        }}
    }}
    
    // 使用共享内存的矩阵转置kernel
    __global__ void sharedMemoryTranspose(float* input, float* output, int n) {{
        __shared__ float tile[TILE_SIZE][TILE_SIZE + 1]; // +1避免bank conflict
        
        int x = blockIdx.x * TILE_SIZE + threadIdx.x;
        int y = blockIdx.y * TILE_SIZE + threadIdx.y;
        
        // 读取数据到共享内存
        if (x < n && y < n) {{
            tile[threadIdx.y][threadIdx.x] = input[y * n + x];
        }}
        
        __syncthreads();
        
        // 计算转置后的位置
        x = blockIdx.y * TILE_SIZE + threadIdx.x;
        y = blockIdx.x * TILE_SIZE + threadIdx.y;
        
        // 写入转置后的数据
        if (x < n && y < n) {{
            output[y * n + x] = tile[threadIdx.x][threadIdx.y];
        }}
    }}
    """
    
    # 编译CUDA kernel
    mod = SourceModule(kernel_code)
    naive_transpose = mod.get_function("naiveTranspose")
    shared_transpose = mod.get_function("sharedMemoryTranspose")
    
    # 分配GPU内存
    d_input = gpuarray.to_gpu(h_input)
    d_output = gpuarray.empty((n, n), dtype=np.float32)
    
    print("\n性能测试结果：")
    print("-" * 40)
    
    # CPU转置基准
    start_time = time.perf_counter()
    h_cpu_output = h_input.T.copy()
    cpu_time = (time.perf_counter() - start_time) * 1000
    print(f"CPU转置时间: {cpu_time:.3f} ms")
    
    if n <= 8:
        print("CPU转置结果 A^T:")
        for i in range(n):
            for j in range(n):
                print(f"{h_cpu_output[i, j]:6.1f}", end=" ")
            print()
        print()
    
    # 测试朴素转置
    block_size = (16, 16, 1)
    grid_size = ((n + 15) // 16, (n + 15) // 16, 1)
    
    # 预热
    naive_transpose(d_input, d_output, np.int32(n), block=block_size, grid=grid_size)
    cuda.Context.synchronize()
    
    # 计时
    start_time = time.perf_counter()
    naive_transpose(d_input, d_output, np.int32(n), block=block_size, grid=grid_size)
    cuda.Context.synchronize()
    naive_time = (time.perf_counter() - start_time) * 1000
    
    # 验证结果
    h_naive_output = d_output.get()
    naive_correct = np.allclose(h_cpu_output, h_naive_output, atol=1e-5)
    print(f"朴素转置时间: {naive_time:.3f} ms {'✓ 正确' if naive_correct else '✗ 错误'}")
    
    # 测试共享内存转置
    shared_block_size = (TILE_SIZE, TILE_SIZE, 1)
    shared_grid_size = ((n + TILE_SIZE - 1) // TILE_SIZE, (n + TILE_SIZE - 1) // TILE_SIZE, 1)
    
    # 预热
    shared_transpose(d_input, d_output, np.int32(n), block=shared_block_size, grid=shared_grid_size)
    cuda.Context.synchronize()
    
    # 计时
    start_time = time.perf_counter()
    shared_transpose(d_input, d_output, np.int32(n), block=shared_block_size, grid=shared_grid_size)
    cuda.Context.synchronize()
    shared_time = (time.perf_counter() - start_time) * 1000
    
    # 验证结果
    h_shared_output = d_output.get()
    shared_correct = np.allclose(h_cpu_output, h_shared_output, atol=1e-5)
    print(f"共享内存转置时间: {shared_time:.3f} ms {'✓ 正确' if shared_correct else '✗ 错误'}")
    
    if n <= 8:
        print("GPU转置结果 A^T:")
        for i in range(n):
            for j in range(n):
                print(f"{h_shared_output[i, j]:6.1f}", end=" ")
            print()
        print()
    
    # 性能分析
    print("\n加速比分析：")
    print("-" * 40)
    if naive_time > 0:
        print(f"朴素转置相对CPU加速比: {cpu_time / naive_time:.2f}x")
    if shared_time > 0:
        print(f"共享内存转置相对CPU加速比: {cpu_time / shared_time:.2f}x")
    if shared_time > 0 and naive_time > 0:
        print(f"共享内存相对朴素算法加速比: {naive_time / shared_time:.2f}x")
    
    print(f"\n算法特点分析：")
    print("- CPU转置：NumPy优化的单线程转置")
    print("- 朴素GPU转置：直接全局内存访问，存在内存合并问题")
    print("- 共享内存GPU转置：使用分块和共享内存优化，减少全局内存访问")
    
    return 0

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"错误: {e}")
        print("请确保系统安装了CUDA和PyCUDA")
