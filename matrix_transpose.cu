#include <stdio.h>
#include <stdlib.h>
#include <cuda_runtime.h>
#include <time.h>

#define TILE_SIZE 16

// 获取时间（毫秒）
double getTime() {
    return (double)clock() / CLOCKS_PER_SEC * 1000.0;
}

// 朴素矩阵转置kernel
__global__ void naiveTranspose(float* input, float* output, int n) {
    int col = blockIdx.x * blockDim.x + threadIdx.x;
    int row = blockIdx.y * blockDim.y + threadIdx.y;

    if (row < n && col < n) {
        output[col * n + row] = input[row * n + col];
    }
}

// 使用共享内存的矩阵转置kernel
__global__ void sharedMemoryTranspose(float* input, float* output, int n) {
    __shared__ float tile[TILE_SIZE][TILE_SIZE + 1];

    int x = blockIdx.x * TILE_SIZE + threadIdx.x;
    int y = blockIdx.y * TILE_SIZE + threadIdx.y;

    if (x < n && y < n) {
        tile[threadIdx.y][threadIdx.x] = input[y * n + x];
    }

    __syncthreads();

    x = blockIdx.y * TILE_SIZE + threadIdx.x;
    y = blockIdx.x * TILE_SIZE + threadIdx.y;

    if (x < n && y < n) {
        output[y * n + x] = tile[threadIdx.x][threadIdx.y];
    }
}

// CPU矩阵转置
void cpuTranspose(float* input, float* output, int n) {
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            output[j * n + i] = input[i * n + j];
        }
    }
}

// 初始化矩阵
void initMatrix(float* matrix, int n) {
    for (int i = 0; i < n * n; i++) {
        matrix[i] = (float)(rand() % 100) / 10.0f;
    }
}

// 验证结果
bool verifyResult(float* cpu_result, float* gpu_result, int n) {
    for (int i = 0; i < n * n; i++) {
        if (fabs(cpu_result[i] - gpu_result[i]) > 1e-5) {
            return false;
        }
    }
    return true;
}

// 打印矩阵
void printMatrix(float* matrix, int n, const char* name) {
    if (n <= 8) {
        printf("%s:\n", name);
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                printf("%6.1f ", matrix[i * n + j]);
            }
            printf("\n");
        }
        printf("\n");
    }
}

int main() {
    int n;
    printf("请输入矩阵大小 n (取值范围[512, 2048]): ");
    scanf("%d", &n);

    if (n < 512 || n > 2048) {
        printf("错误：矩阵大小必须在[512, 2048]范围内\n");
        return -1;
    }

    printf("矩阵大小: %d × %d\n", n, n);

    // 分配内存
    size_t size = n * n * sizeof(float);
    float* h_input = (float*)malloc(size);
    float* h_output = (float*)malloc(size);
    float* h_cpu_output = (float*)malloc(size);

    // 初始化矩阵
    srand(time(NULL));
    initMatrix(h_input, n);
    printMatrix(h_input, n, "原始矩阵 A");

    // CPU转置
    double cpu_start = getTime();
    cpuTranspose(h_input, h_cpu_output, n);
    double cpu_time = getTime() - cpu_start;
    printMatrix(h_cpu_output, n, "CPU转置结果 A^T");

    // GPU内存分配
    float *d_input, *d_output;
    cudaMalloc(&d_input, size);
    cudaMalloc(&d_output, size);
    cudaMemcpy(d_input, h_input, size, cudaMemcpyHostToDevice);

    printf("\n性能测试结果：\n");
    printf("CPU转置时间: %.2f ms\n", cpu_time);

    // 测试朴素转置
    dim3 blockDim(16, 16);
    dim3 gridDim((n + 15) / 16, (n + 15) / 16);

    double gpu_start = getTime();
    naiveTranspose<<<gridDim, blockDim>>>(d_input, d_output, n);
    cudaDeviceSynchronize();
    double naive_time = getTime() - gpu_start;

    cudaMemcpy(h_output, d_output, size, cudaMemcpyDeviceToHost);
    printf("朴素转置时间: %.2f ms", naive_time);
    if (verifyResult(h_cpu_output, h_output, n)) {
        printf(" ✓ 正确\n");
    } else {
        printf(" ✗ 错误\n");
    }

    // 测试共享内存转置
    dim3 sharedBlockDim(TILE_SIZE, TILE_SIZE);
    dim3 sharedGridDim((n + TILE_SIZE - 1) / TILE_SIZE, (n + TILE_SIZE - 1) / TILE_SIZE);

    gpu_start = getTime();
    sharedMemoryTranspose<<<sharedGridDim, sharedBlockDim>>>(d_input, d_output, n);
    cudaDeviceSynchronize();
    double shared_time = getTime() - gpu_start;

    cudaMemcpy(h_output, d_output, size, cudaMemcpyDeviceToHost);
    printf("共享内存转置时间: %.2f ms", shared_time);
    if (verifyResult(h_cpu_output, h_output, n)) {
        printf(" ✓ 正确\n");
    } else {
        printf(" ✗ 错误\n");
    }

    printMatrix(h_output, n, "GPU转置结果 A^T");

    printf("\n加速比分析：\n");
    printf("朴素转置加速比: %.2fx\n", cpu_time / naive_time);
    printf("共享内存转置加速比: %.2fx\n", cpu_time / shared_time);

    // 清理内存
    free(h_input);
    free(h_output);
    free(h_cpu_output);
    cudaFree(d_input);
    cudaFree(d_output);

    return 0;
}
